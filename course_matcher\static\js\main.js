// Custom JavaScript for the CourseRec application

console.log("main.js loaded");

// Enhanced Navigation Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize navigation state management
    initializeNavigation();

    // Initialize accessibility features
    initializeAccessibility();

    // Initialize responsive behavior
    initializeResponsiveBehavior();
});

function initializeNavigation() {
    // Handle keyboard navigation for sidebar toggle
    document.addEventListener('keydown', function(e) {
        // Toggle sidebar with Ctrl+B
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            const sidebarToggle = document.querySelector('[aria-label="Toggle sidebar"]');
            if (sidebarToggle) {
                sidebarToggle.click();
            }
        }

        // Close mobile menu with Escape key
        if (e.key === 'Escape') {
            const mobileMenuOpen = document.body.getAttribute('x-data');
            if (mobileMenuOpen && mobileMenuOpen.includes('isMobileMenuOpen: true')) {
                // Trigger Alpine.js to close mobile menu
                window.dispatchEvent(new CustomEvent('close-mobile-menu'));
            }
        }
    });

    // Handle focus management for mobile menu
    document.addEventListener('click', function(e) {
        const mobileToggle = e.target.closest('[aria-controls="mobile-menu"]');
        if (mobileToggle) {
            // Focus management for mobile menu
            setTimeout(() => {
                const mobileMenu = document.querySelector('#mobile-menu, .mobile-nav-panel');
                if (mobileMenu && mobileMenu.style.display !== 'none') {
                    const firstFocusable = mobileMenu.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
                    if (firstFocusable) {
                        firstFocusable.focus();
                    }
                }
            }, 100);
        }
    });
}

function initializeAccessibility() {
    // Add ARIA labels and roles where needed
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        if (!item.getAttribute('role')) {
            item.setAttribute('role', 'menuitem');
        }

        // Add keyboard navigation
        item.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                item.click();
            }
        });
    });

    // Enhance focus visibility
    const focusableElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.classList.add('focus-visible');
        });

        element.addEventListener('blur', function() {
            this.classList.remove('focus-visible');
        });
    });
}

function initializeResponsiveBehavior() {
    // Handle window resize events
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            handleResponsiveChanges();
        }, 250);
    });

    // Initial responsive setup
    handleResponsiveChanges();
}

function handleResponsiveChanges() {
    const isDesktop = window.innerWidth >= 1024;
    const isMobile = window.innerWidth < 768;

    // Update body classes for responsive styling
    document.body.classList.toggle('is-desktop', isDesktop);
    document.body.classList.toggle('is-mobile', isMobile);

    // Close mobile menu on desktop
    if (isDesktop) {
        const mobileMenuToggle = document.querySelector('[aria-controls="mobile-menu"]');
        if (mobileMenuToggle && mobileMenuToggle.getAttribute('aria-expanded') === 'true') {
            mobileMenuToggle.click();
        }
    }

    // Adjust sidebar behavior
    const sidebar = document.querySelector('.nav-sidebar');
    if (sidebar) {
        if (isDesktop) {
            sidebar.classList.remove('mobile-hidden');
        } else {
            sidebar.classList.add('mobile-hidden');
        }
    }
}

// Enhanced HTMX integration
document.addEventListener('htmx:configRequest', function(evt) {
    // Add loading states
    const target = evt.target;
    target.classList.add('htmx-loading');

    // Add CSRF token
    evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
});

document.addEventListener('htmx:afterRequest', function(evt) {
    // Remove loading states
    const target = evt.target;
    target.classList.remove('htmx-loading');

    // Handle errors gracefully
    if (evt.detail.xhr.status >= 400) {
        console.error('HTMX request failed:', evt.detail.xhr.responseText);
        showNotification('An error occurred. Please try again.', 'error');
    }
});

// Notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${getNotificationClasses(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${getNotificationIcon(type)}
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after duration
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, duration);
}

function getNotificationClasses(type) {
    const classes = {
        'info': 'bg-blue-50 text-blue-800 border border-blue-200',
        'success': 'bg-green-50 text-green-800 border border-green-200',
        'warning': 'bg-yellow-50 text-yellow-800 border border-yellow-200',
        'error': 'bg-red-50 text-red-800 border border-red-200'
    };
    return classes[type] || classes.info;
}

function getNotificationIcon(type) {
    const icons = {
        'info': '<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>',
        'success': '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        'warning': '<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        'error': '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'
    };
    return icons[type] || icons.info;
}

// Three.js visualizations will be initialized here
