{% extends 'base.html' %}

{% block title %}Manage Students - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" x-data="{ isModalOpen: false }">

    <!-- Header -->
    <div class="mb-8">
        <h1 class="heading-primary">Student Management</h1>
        <p class="mt-2 text-muted">Manage student records and academic information.</p>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h2 class="heading-tertiary">Students</h2>
                <!-- Add Student Button -->
                <button @click="isModalOpen = true" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Student
                </button>
            </div>
        </div>

    <!-- Modal -->
    <div x-show="isModalOpen"
         x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"
         @keydown.escape.window="isModalOpen = false">
        
        <div @click.away="isModalOpen = false" class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4 border-b pb-3">
                <h3 class="text-xl font-semibold text-gray-800">Add New Student</h3>
                <button @click="isModalOpen = false" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
            </div>
            <div class="mt-4">
                <p class="text-gray-600 mb-6">This is where the form to add a new student would go. For now, it's a demonstration of an Alpine.js modal.</p>
            </div>
            <div class="card-footer">
                <div class="flex justify-end space-x-4">
                    <button @click="isModalOpen = false" class="btn btn-secondary">Cancel</button>
                    <button class="btn btn-primary">Save Student</button>
                </div>
            </div>
        </div>
    </div>

        <!-- Search and Filter -->
        <div class="card-body">
            <div class="form-group">
                <label for="student-search" class="form-label">Search Students</label>
                <input type="text"
                       id="student-search"
                       placeholder="Search students by name, ID, or major..."
                       class="form-input"
                       hx-post="/api/search-students/"
                       hx-trigger="keyup changed delay:500ms"
                       hx-target="#student-list"
                       hx-indicator=".htmx-indicator">
            </div>

            <!-- Student List -->
            <div id="student-list" hx-get="/api/student-list/" hx-trigger="load">
                <div class="htmx-indicator flex items-center justify-center py-8">
                    <div class="loading-spinner mr-3"></div>
                    <span class="text-muted">Loading student list...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
