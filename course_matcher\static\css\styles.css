/* Unified Design System for CourseRec Application */

/* ===== DESIGN TOKENS ===== */
:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-500: #10b981;
    --success-600: #059669;
    --success-700: #047857;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Typography */
    --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* ===== UTILITY CLASSES ===== */

/* Hide elements with x-cloak */
[x-cloak] {
    display: none !important;
}

/* HTMX Loading States */
.htmx-indicator {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

/* Focus States for Accessibility */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* ===== COMPONENT STYLES ===== */

/* Unified Card Component */
.card {
    @apply bg-white rounded-lg shadow-md transition-all duration-300;
}

.card-hover {
    @apply hover:shadow-lg hover:-translate-y-1;
}

.card-gradient {
    @apply bg-gradient-to-br from-white to-blue-50;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Unified Button Components */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

/* Form Components */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
}

.form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
}

.form-checkbox {
    @apply rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
    @apply text-sm text-red-600 mt-1;
}

/* Badge Components */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-primary-100 text-primary-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-gray {
    @apply bg-gray-100 text-gray-800;
}

/* Navigation Components */
.nav-sidebar {
    @apply fixed inset-y-0 left-0 z-40 bg-gray-800 text-white transition-all duration-300;
}

.nav-sidebar-collapsed {
    @apply w-20;
}

.nav-sidebar-expanded {
    @apply w-64;
}

.nav-item {
    @apply flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-700 group transition-colors duration-200;
}

.nav-item-active {
    @apply bg-gray-700 text-white;
}

.nav-icon {
    @apply flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white;
}

.nav-tooltip {
    @apply absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50;
}

/* Mobile Navigation */
.mobile-nav-overlay {
    @apply fixed inset-0 z-40 bg-gray-600 bg-opacity-75 transition-opacity;
}

.mobile-nav-panel {
    @apply relative flex-1 flex flex-col max-w-xs w-full bg-gray-800;
}

.mobile-nav-toggle {
    @apply inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-white transition-all duration-200;
}

/* Stats/Metrics Cards */
.stat-card {
    @apply bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg;
}

.stat-card-gradient {
    @apply bg-gradient-to-br from-white to-blue-50 p-6 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

.stat-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center shadow-inner;
}

.stat-icon-primary {
    @apply bg-blue-100 text-blue-600;
}

.stat-icon-success {
    @apply bg-green-100 text-green-600;
}

.stat-icon-warning {
    @apply bg-yellow-100 text-yellow-600;
}

.stat-icon-danger {
    @apply bg-red-100 text-red-600;
}

.stat-value {
    @apply text-2xl font-bold text-gray-900;
}

.stat-label {
    @apply text-sm font-medium text-gray-500 truncate;
}

/* Grid Layouts */
.dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.content-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* Typography Utilities */
.heading-primary {
    @apply text-3xl font-bold text-gray-900;
}

.heading-secondary {
    @apply text-2xl font-semibold text-gray-800;
}

.heading-tertiary {
    @apply text-xl font-semibold text-gray-700;
}

.text-muted {
    @apply text-gray-600;
}

.text-subtle {
    @apply text-gray-500;
}

/* Loading States */
.loading-spinner {
    @apply animate-spin h-5 w-5 text-white;
}

.loading-overlay {
    @apply fixed top-4 right-4 z-50 bg-primary-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }

    .mobile-full-width {
        width: 100% !important;
    }

    .mobile-stack {
        flex-direction: column !important;
    }
}

@media (min-width: 1024px) {
    .desktop-only {
        display: block !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus visible for better keyboard navigation */
.focus-visible:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 1px solid var(--gray-300);
    }

    .btn {
        border: 1px solid currentColor;
    }
}

/* Enhanced Interactive States */
.interactive-element {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.interactive-element:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Loading States */
.htmx-loading {
    position: relative;
    pointer-events: none;
}

.htmx-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.htmx-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-200);
    border-top: 2px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Styles */
.form-group {
    @apply mb-6;
}

.form-input:focus {
    @apply ring-2 ring-primary-500 ring-opacity-50 border-primary-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    @apply border-red-500;
}

.form-input:invalid:focus {
    @apply ring-red-500 border-red-500;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Enhanced Button States */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:disabled {
    @apply opacity-50 cursor-not-allowed;
    pointer-events: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-600));
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--gray-300), var(--gray-200));
}

/* Card Enhancements */
.card-interactive {
    @apply cursor-pointer transition-all duration-300;
}

.card-interactive:hover {
    @apply shadow-xl;
    transform: translateY(-2px);
}

.card-interactive:active {
    transform: translateY(0);
}

/* Navigation Enhancements */
.nav-item:hover {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.8), rgba(75, 85, 99, 0.6));
}

.nav-item:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.nav-item-active {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-600));
    color: white;
}

.nav-item-active .nav-icon {
    color: white;
}

/* Tooltip Enhancements */
.nav-tooltip {
    backdrop-filter: blur(8px);
    box-shadow: var(--shadow-lg);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .stat-card-gradient {
        @apply p-4;
    }

    .stat-value {
        @apply text-xl;
    }

    .heading-primary {
        @apply text-2xl;
    }

    .heading-secondary {
        @apply text-xl;
    }

    .btn {
        @apply px-3 py-2 text-sm;
    }

    .card-body {
        @apply p-4;
    }

    .dashboard-grid {
        @apply grid-cols-1 gap-4;
    }

    .content-grid {
        @apply grid-cols-1 gap-4;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        @apply py-3 px-4 text-base;
        min-height: 44px;
    }

    .nav-item {
        @apply py-3;
        min-height: 44px;
    }

    .mobile-nav-toggle {
        @apply p-3;
        min-width: 44px;
        min-height: 44px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .card-hover:hover {
        transform: none;
    }

    .stat-card-gradient:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .nav-sidebar,
    .mobile-nav-panel,
    .mobile-nav-toggle,
    .btn,
    .htmx-indicator {
        display: none !important;
    }

    .card {
        @apply border border-gray-300;
        box-shadow: none;
    }

    .heading-primary,
    .heading-secondary,
    .heading-tertiary {
        color: black !important;
    }
}
