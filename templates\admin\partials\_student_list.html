<table class="min-w-full bg-white">
    <thead class="bg-gray-100">
        <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Major</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GPA</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for student in students %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ student.user.get_full_name|default:student.user.username }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ student.student_id }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ student.major.name|default:'N/A' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ student.get_year_display }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ student.gpa|floatformat:2 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="#" class="text-primary-600 hover:text-primary-900">View</a>
                </td>
            </tr>
        {% empty %}
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No students found.</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
