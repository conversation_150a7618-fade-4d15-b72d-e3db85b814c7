{% extends 'base.html' %}

{% block title %}Login - {{ block.super }}{% endblock %}

{% block main_classes %}flex-grow flex items-center justify-center p-6{% endblock %}

{% block sidebar %}
{% endblock %}

{% block content %}
<div class="max-w-md w-full">
    <div class="bg-white p-8 rounded-lg shadow-md">
        <h2 class="text-2xl font-bold text-center text-gray-800 mb-6">Log In</h2>
        
        {% if form.errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Error:</strong>
                <span class="block sm:inline">Your username and password didn't match. Please try again.</span>
            </div>
        {% endif %}

        <form method="post" action="{% url 'login' %}">
            {% csrf_token %}
            <div class="mb-4">
                <label for="id_username" class="block text-sm font-medium text-gray-700">Username</label>
                <input type="text" name="username" id="id_username" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
            </div>
            
            <div class="mb-6">
                <label for="id_password" class="block text-sm font-medium text-gray-700">Password</label>
                <input type="password" name="password" id="id_password" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
            </div>
            
            <div>
                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Log In
                </button>
            </div>
            <input type="hidden" name="next" value="{{ next }}" />
        </form>
    </div>
</div>
{% endblock %}
