<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Course Recommendation System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10/dist/htmx.min.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }
        
        .htmx-indicator {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        
        .up-current {
            background-color: rgb(239 246 255) !important;
            color: rgb(29 78 216) !important;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .three-canvas {
            border-radius: 0.5rem;
        }
    </style>
    
    {% load static %}
        <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body class="h-full bg-gray-100"
      x-data="{
          isSidebarOpen: false,
          isMobileMenuOpen: false,
          mobileMenuOpen: false,
          isDesktop: false
      }"
      x-init="
          isDesktop = window.innerWidth >= 1024;
          isSidebarOpen = isDesktop;
          $watch('isMobileMenuOpen', value => { 
              if(value) {
                  document.body.classList.add('overflow-y-hidden');
              } else {
                  document.body.classList.remove('overflow-y-hidden');
              }
          });
      "
      @resize.window.debounce.200ms="
          isDesktop = window.innerWidth >= 1024;
          if (isDesktop) {
              isMobileMenuOpen = false;
          } else {
              isSidebarOpen = false;
          }
      ">
    <!-- Mobile sidebar backdrop -->
    <div x-show="isMobileMenuOpen" @click="isMobileMenuOpen = false"
         class="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 lg:hidden"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         x-cloak>
    </div>
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 w-full z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Left side: Logo and mobile toggle -->
                <div class="flex items-center">
                    <!-- Mobile menu button -->
                    <button @click="isMobileMenuOpen = !isMobileMenuOpen" type="button" x-ref="mobileToggleButton" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white lg:hidden" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <template x-if="!isMobileMenuOpen">
                            <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </template>
                        <template x-if="isMobileMenuOpen">
                            <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </template>
                    </button>
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center ml-2">
                        <a href="{% url 'home' %}" class="flex items-center text-gray-800">
                            <svg class="h-8 w-auto text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="ml-3 text-xl font-bold">CourseRec</span>
                        </a>
                    </div>
                </div>

                <!-- Right side: Desktop User Menu -->
                <div class="hidden lg:flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <span class="text-sm font-medium text-gray-600">Welcome, {{ user.get_full_name|default:user.username }}</span>
                        <a href="{% url 'logout' %}" 
                           class="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">
                            Logout
                        </a>
                    {% else %}
                        <a href="{% url 'login' %}" 
                           class="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">
                            Login
                        </a>
                    {% endif %}
                </div>
                

    </nav>

    <!-- Main content wrapper -->
    <div class="flex pt-16">
        <!-- Sidebar -->
        {% block sidebar %}{% endblock %}
        
        <!-- Main content -->
        <main class="flex-1 {% block main_classes %}p-6{% endblock %}" {% block main_dynamic_classes %}{% endblock %}>
            <!-- Loading indicator -->
            <div class="htmx-indicator fixed top-4 right-4 z-50">
                <div class="bg-primary-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading...
                </div>
            </div>
            
            <!-- Flash messages -->
            {% if messages %}
                <div class="mb-6">
                    {% for message in messages %}
                        <div class="rounded-md p-4 mb-4 {{ message.tags|default:'info' }}"
                             x-data="{ show: true }"
                             x-show="show"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform scale-90"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-300"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-90">
                            <div class="flex justify-between items-center">
                                <div class="flex">
                                    <div class="ml-3">
                                        <p class="text-sm font-medium">{{ message }}</p>
                                    </div>
                                </div>
                                <button @click="show = false" class="ml-4 text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Scroll to top button -->
    <div x-data="{
        show: false,
        init() {
            window.addEventListener('scroll', () => {
                this.show = window.scrollY > 300;
            });
        }
    }" x-show="show" x-transition.opacity.duration.300ms
         class="fixed bottom-4 right-4 z-50">
        <button @click="window.scrollTo({ top: 0, behavior: 'smooth' })"
                class="bg-primary-600 hover:bg-primary-700 text-white rounded-full p-3 shadow-lg transition-transform transform hover:scale-110">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
            </svg>
        </button>
    </div>

    <!-- HTMX Configuration -->
    <script>
        // HTMX event listeners
        document.body.addEventListener('htmx:configRequest', (event) => {
            event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });
        
        // HTMX success/error handling
        document.body.addEventListener('htmx:afterRequest', (event) => {
            if (event.detail.xhr.status >= 400) {
                console.error('HTMX request failed:', event.detail.xhr.responseText);
            }
        });
    </script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
