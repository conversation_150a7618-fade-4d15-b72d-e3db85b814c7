{% extends 'base.html' %}

{% block title %}Admin Dashboard - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Welcome Header -->
    <div class="mb-8">
        <h1 class="heading-primary">Admin Dashboard</h1>
        <p class="mt-2 text-muted">Monitor system performance and manage course recommendations.</p>
    </div>

    <!-- Quick Stats -->
    <div class="dashboard-grid mb-8">
        <div class="stat-card-gradient">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="stat-icon stat-icon-primary">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="stat-label">Total Students</dt>
                        <dd class="stat-value">1,234</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="stat-card-gradient">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="stat-icon stat-icon-success">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="stat-label">Total Courses</dt>
                        <dd class="stat-value">567</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="stat-card-gradient">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="stat-icon stat-icon-warning">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="stat-label">Advising Sessions</dt>
                        <dd class="stat-value">89</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="stat-card-gradient">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="stat-icon stat-icon-danger">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="stat-label">Pending Reports</dt>
                        <dd class="stat-value">12</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Recent Activity Section -->
    <div class="mt-8">
        <h2 class="heading-secondary mb-6">Recent Activity</h2>
        <div class="card">
            <div class="card-header">
                <h3 class="heading-tertiary">System Activity Feed</h3>
            </div>
            <div class="card-body">
                <!-- Activity feed will be loaded dynamically here -->
                <div hx-get="/admin/api/recent-activity/" hx-trigger="load" hx-swap="innerHTML">
                    <div class="htmx-indicator flex items-center justify-center py-8">
                        <div class="loading-spinner mr-3"></div>
                        <span class="text-muted">Loading recent activity...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="mt-8">
        <h2 class="heading-secondary mb-6">Quick Actions</h2>
        <div class="content-grid">
            <div class="card card-hover">
                <div class="card-body text-center">
                    <div class="stat-icon stat-icon-primary mx-auto mb-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h3 class="heading-tertiary mb-2">Add Student</h3>
                    <p class="text-muted mb-4">Register a new student in the system</p>
                    <a href="{% url 'admin_students' %}" class="btn btn-primary">
                        Add Student
                    </a>
                </div>
            </div>

            <div class="card card-hover">
                <div class="card-body text-center">
                    <div class="stat-icon stat-icon-success mx-auto mb-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="heading-tertiary mb-2">Manage Courses</h3>
                    <p class="text-muted mb-4">Add or edit course information</p>
                    <a href="{% url 'admin_courses' %}" class="btn btn-success">
                        Manage Courses
                    </a>
                </div>
            </div>

            <div class="card card-hover">
                <div class="card-body text-center">
                    <div class="stat-icon stat-icon-warning mx-auto mb-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="heading-tertiary mb-2">View Reports</h3>
                    <p class="text-muted mb-4">Generate system analytics and reports</p>
                    <a href="{% url 'admin_reports' %}" class="btn btn-warning">
                        View Reports
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
