from django.urls import path
from . import views

urlpatterns = [
    # Student-facing URL (can be expanded later)
    path('', views.home, name='home'),
    path('academic-records/', views.student_academic_records, name='academic_records'),
    path('interests/', views.student_interests, name='student_interests'),
    path('career-goals/', views.career_goals, name='career_goals'),
    path('recommendations/', views.recommendations, name='recommendations'),

    # Student HTMX actions
    path('bookmark-course/<int:course_id>/', views.bookmark_course, name='bookmark_course'),
    path('delete-interest/<int:interest_id>/', views.delete_interest, name='delete_interest'),
    path('add-interest/', views.add_interest, name='add_interest'),
    path('add-suggested-interest/', views.add_suggested_interest, name='add_suggested_interest'),
    path('update-career-goal/<int:goal_id>/', views.update_career_goal, name='update_career_goal'),
    path('add-career-goal/', views.add_career_goal, name='add_career_goal'),
    path('delete-career-goal/<int:goal_id>/', views.delete_career_goal, name='delete_career_goal'),
    path('delete-academic-record/<int:record_id>/', views.delete_academic_record, name='delete_academic_record'),
    path('add-academic-record/', views.add_academic_record, name='add_academic_record'),


    # Admin URLs
    path('dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('dashboard/students/', views.admin_students, name='admin_students'),
    path('dashboard/courses/', views.admin_courses, name='admin_courses'),
    path('dashboard/advising/', views.admin_advising, name='admin_advising'),
    path('dashboard/reports/', views.admin_reports, name='admin_reports'),

    # HTMX API URLs
    path('api/recent-activity/', views.recent_activity_api, name='api_recent_activity'),
    path('api/student-list/', views.student_list_api, name='api_student_list'),
    path('api/search-students/', views.search_students_api, name='api_search_students'),
    path('api/course-list/', views.course_list_api, name='api_course_list'),
    path('api/search-courses/', views.search_courses_api, name='api_search_courses'),
]
