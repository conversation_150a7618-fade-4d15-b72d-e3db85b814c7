{% extends 'student/base.html' %}

{% block title %}Course Recommendations - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" 
     x-data="recommendationsManager()" 
     x-init="init()">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Course Recommendations</h1>
        <p class="mt-2 text-gray-600">Personalized course suggestions based on your academic history, interests, and career goals.</p>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Filter Recommendations</h2>
        </div>
        
        <div class="p-6">
            <form hx-get="{% url 'recommendations' %}"
                  hx-target="#recommendations-container"
                  hx-swap="innerHTML"
                  hx-trigger="change"
                  class="space-y-4">
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">
                            Difficulty Level
                        </label>
                        <select id="difficulty_level" name="difficulty_level" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">All Levels</option>
                            <option value="beginner" {% if request.GET.difficulty_level == 'beginner' %}selected{% endif %}>Beginner</option>
                            <option value="intermediate" {% if request.GET.difficulty_level == 'intermediate' %}selected{% endif %}>Intermediate</option>
                            <option value="advanced" {% if request.GET.difficulty_level == 'advanced' %}selected{% endif %}>Advanced</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="credits" class="block text-sm font-medium text-gray-700 mb-2">
                            Credits
                        </label>
                        <select id="credits" name="credits" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Any Credits</option>
                            <option value="1-2" {% if request.GET.credits == '1-2' %}selected{% endif %}>1-2 Credits</option>
                            <option value="3-4" {% if request.GET.credits == '3-4' %}selected{% endif %}>3-4 Credits</option>
                            <option value="5-6" {% if request.GET.credits == '5-6' %}selected{% endif %}>5+ Credits</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
                            Department
                        </label>
                        <select id="department" name="department" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                            <option value="{{ dept.code }}" {% if request.GET.department == dept.code %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="semester" class="block text-sm font-medium text-gray-700 mb-2">
                            Semester
                        </label>
                        <select id="semester" name="semester" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Any Semester</option>
                            <option value="fall" {% if request.GET.semester == 'fall' %}selected{% endif %}>Fall</option>
                            <option value="spring" {% if request.GET.semester == 'spring' %}selected{% endif %}>Spring</option>
                            <option value="summer" {% if request.GET.semester == 'summer' %}selected{% endif %}>Summer</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="prerequisites_met" value="true" 
                                   {% if request.GET.prerequisites_met %}checked{% endif %}
                                   class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Only show courses with met prerequisites</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <label for="sort_by" class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select id="sort_by" name="sort_by" 
                                class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="relevance" {% if request.GET.sort_by == 'relevance' or not request.GET.sort_by %}selected{% endif %}>Relevance</option>
                            <option value="rating" {% if request.GET.sort_by == 'rating' %}selected{% endif %}>Rating</option>
                            <option value="credits" {% if request.GET.sort_by == 'credits' %}selected{% endif %}>Credits</option>
                            <option value="difficulty" {% if request.GET.sort_by == 'difficulty' %}selected{% endif %}>Difficulty</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Recommendations Grid -->
    <div id="recommendations-container">
        {% if recommendations %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for rec in recommendations %}
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ rec.course.name }}</h3>
                            <p class="text-sm text-blue-600 font-medium">{{ rec.course.code }}</p>
                        </div>
                        <div class="flex items-center space-x-1 ml-3">
                            <div class="flex items-center">
                                {% for i in "12345"|make_list %}
                                    {% if forloop.counter <= rec.relevance_score|floatformat:0 %}
                                        <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    {% else %}
                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="text-xs text-gray-500 ml-1">{{ rec.relevance_score|floatformat:1 }}</span>
                        </div>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-4 line-clamp-3">{{ rec.course.description }}</p>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ rec.course.credits }} Credits
                            </span>
                            {% if rec.course.difficulty_level %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                       {% if rec.course.difficulty_level == 'beginner' %}bg-green-100 text-green-800
                                       {% elif rec.course.difficulty_level == 'intermediate' %}bg-yellow-100 text-yellow-800
                                       {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ rec.course.get_difficulty_level_display }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if rec.reason %}
                    <div class="mb-4 p-3 bg-green-50 rounded-lg">
                        <p class="text-xs text-green-700">
                            <span class="font-medium">Why recommended:</span> {{ rec.reason }}
                        </p>
                    </div>
                    {% endif %}
                    
                    {% if rec.course.prerequisites.all %}
                    <div class="mb-4">
                        <h4 class="text-xs font-medium text-gray-700 mb-2">Prerequisites:</h4>
                        <div class="flex flex-wrap gap-1">
                            {% for prereq in rec.course.prerequisites.all %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium 
                                       {% if prereq in completed_courses %}bg-green-100 text-green-800
                                       {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ prereq.code }}
                                {% if prereq in completed_courses %}
                                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                {% else %}
                                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                {% endif %}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <button hx-post="{% url 'bookmark_course' rec.course.id %}"
                                    hx-target="this"
                                    hx-swap="outerHTML"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                </svg>
                                Bookmark
                            </button>
                            
                            <button @click="showCourseDetails({{ rec.course.id }})"
                                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 transition-colors">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Details
                            </button>
                        </div>
                        
                        {% if rec.course.registration_link %}
                        <a href="{{ rec.course.registration_link }}" 
                           target="_blank"
                           class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                            Register
                            <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Load More Button -->
        {% if has_more %}
        <div class="mt-8 text-center">
            <button hx-get="{% url 'recommendations' %}?page={{ next_page }}"
                    hx-target="#recommendations-container"
                    hx-swap="beforeend"
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 transition-colors">
                Load More Recommendations
                <div class="htmx-indicator ml-2">
                    <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </button>
        </div>
        {% endif %}
        
        {% else %}
        <div class="bg-white rounded-lg shadow">
            <div class="p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No recommendations available</h3>
                <p class="mt-1 text-sm text-gray-500">
                    We need more information about you to provide personalized recommendations.
                </p>
                <div class="mt-6 flex justify-center space-x-3">
                    <a href="{% url 'academic_records' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Add Academic Records
                    </a>
                    <a href="{% url 'student_interests' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Set Interests
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Course Details Modal -->
<div x-show="showModal" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-50 overflow-y-auto" 
     x-cloak>
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="showModal = false">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div id="course-details-modal" class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <!-- Modal content will be loaded here via HTMX -->
            </div>
        </div>
    </div>
</div>

<script>
function recommendationsManager() {
    return {
        showModal: false,
        
        init() {
            // Initialize any required setup
        },
        
        showCourseDetails(courseId) {
            htmx.ajax('GET', `/student/course/${courseId}/details/`, {
                target: '#course-details-modal',
                swap: 'innerHTML'
            });
            this.showModal = true;
        }
    };
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    [x-cloak] {
        display: none !important;
    }
    
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
{% endblock %}
