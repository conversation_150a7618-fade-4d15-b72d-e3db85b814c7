{% extends 'student/base.html' %}

{% block title %}Career Goals - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" 
     x-data="careerGoalsManager()" 
     x-init="init()">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Career Goals</h1>
        <p class="mt-2 text-gray-600">Define your career objectives to receive targeted course recommendations that align with your professional aspirations.</p>
    </div>

    <!-- Primary Career Goal -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Primary Career Goal</h2>
        </div>
        
        <div class="p-6">
            {% if primary_goal %}
            <div class="flex items-start justify-between" x-data="{ editing: false }">
                <div class="flex-1" x-show="!editing">
                    <h3 class="text-lg font-medium text-gray-900">{{ primary_goal.title }}</h3>
                    <p class="text-sm text-gray-600 mt-1">{{ primary_goal.description }}</p>
                    <div class="mt-3 flex items-center space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ primary_goal.get_timeline_display }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ primary_goal.get_industry_display }}
                        </span>
                    </div>
                </div>
                
                <div class="flex-1" x-show="editing" x-cloak>
                    <form hx-put="{% url 'update_career_goal' primary_goal.id %}"
                          hx-target="#primary-goal-container"
                          hx-swap="innerHTML"
                          hx-on::after-request="editing = false"
                          class="space-y-4">
                        <div>
                            <input type="text" 
                                   name="title"
                                   value="{{ primary_goal.title }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md text-lg font-medium focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <textarea name="description" 
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">{{ primary_goal.description }}</textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <select name="timeline" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="immediate" {% if primary_goal.timeline == 'immediate' %}selected{% endif %}>Immediate (0-1 year)</option>
                                <option value="short_term" {% if primary_goal.timeline == 'short_term' %}selected{% endif %}>Short-term (1-3 years)</option>
                                <option value="medium_term" {% if primary_goal.timeline == 'medium_term' %}selected{% endif %}>Medium-term (3-5 years)</option>
                                <option value="long_term" {% if primary_goal.timeline == 'long_term' %}selected{% endif %}>Long-term (5+ years)</option>
                            </select>
                            <select name="industry" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="technology" {% if primary_goal.industry == 'technology' %}selected{% endif %}>Technology</option>
                                <option value="finance" {% if primary_goal.industry == 'finance' %}selected{% endif %}>Finance</option>
                                <option value="healthcare" {% if primary_goal.industry == 'healthcare' %}selected{% endif %}>Healthcare</option>
                                <option value="education" {% if primary_goal.industry == 'education' %}selected{% endif %}>Education</option>
                                <option value="consulting" {% if primary_goal.industry == 'consulting' %}selected{% endif %}>Consulting</option>
                                <option value="research" {% if primary_goal.industry == 'research' %}selected{% endif %}>Research</option>
                                <option value="other" {% if primary_goal.industry == 'other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" 
                                    @click="editing = false"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                                Update Goal
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="ml-4 flex-shrink-0" x-show="!editing">
                    <button @click="editing = true"
                            class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            {% else %}
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No primary career goal set</h3>
                <p class="mt-1 text-sm text-gray-500">Define your main career objective to get better course recommendations.</p>
                <button @click="showPrimaryGoalForm = true"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Set Primary Goal
                </button>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Add Primary Goal Form -->
    <div x-show="showPrimaryGoalForm" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white rounded-lg shadow mb-8" 
         x-cloak>
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Set Primary Career Goal</h2>
        </div>
        
        <form hx-post="{% url 'add_career_goal' %}"
              hx-vals='{"is_primary": "true"}'
              hx-target="#primary-goal-container"
              hx-swap="innerHTML"
              hx-on::after-request="if(event.detail.successful) { this.reset(); showPrimaryGoalForm = false; }"
              class="p-6 space-y-6">
            
            <div>
                <label for="primary_goal_title" class="block text-sm font-medium text-gray-700 mb-2">
                    Career Goal Title
                </label>
                <input type="text" 
                       id="primary_goal_title" 
                       name="title"
                       placeholder="e.g., Software Engineer at Tech Company, Data Scientist, Product Manager"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
            </div>
            
            <div>
                <label for="primary_goal_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="primary_goal_description" 
                          name="description"
                          rows="4"
                          placeholder="Describe your career goal, including specific roles, companies, or achievements you're targeting..."
                          required
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="primary_goal_timeline" class="block text-sm font-medium text-gray-700 mb-2">
                        Timeline
                    </label>
                    <select id="primary_goal_timeline" 
                            name="timeline"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select Timeline</option>
                        <option value="immediate">Immediate (0-1 year)</option>
                        <option value="short_term">Short-term (1-3 years)</option>
                        <option value="medium_term">Medium-term (3-5 years)</option>
                        <option value="long_term">Long-term (5+ years)</option>
                    </select>
                </div>
                
                <div>
                    <label for="primary_goal_industry" class="block text-sm font-medium text-gray-700 mb-2">
                        Industry
                    </label>
                    <select id="primary_goal_industry" 
                            name="industry"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select Industry</option>
                        <option value="technology">Technology</option>
                        <option value="finance">Finance</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="education">Education</option>
                        <option value="consulting">Consulting</option>
                        <option value="research">Research</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button type="button" 
                        @click="showPrimaryGoalForm = false"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                    Set Primary Goal
                    <div class="htmx-indicator ml-2">
                        <svg class="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </button>
            </div>
        </form>
    </div>

    <!-- Secondary Career Goals -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">Secondary Career Goals</h2>
            <button @click="showSecondaryGoalForm = !showSecondaryGoalForm"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Goal
            </button>
        </div>
        
        <div class="p-6">
            <div id="secondary-goals-container" class="space-y-4">
                {% for goal in secondary_goals %}
                <div class="p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-gray-900">{{ goal.title }}</h3>
                            <p class="text-xs text-gray-600 mt-1">{{ goal.description }}</p>
                            <div class="mt-2 flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ goal.get_timeline_display }}
                                </span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                    {{ goal.get_industry_display }}
                                </span>
                            </div>
                        </div>
                        <button hx-delete="{% url 'delete_career_goal' goal.id %}"
                                hx-confirm="Are you sure you want to delete this career goal?"
                                hx-target="closest div"
                                hx-swap="outerHTML"
                                class="ml-4 text-red-400 hover:text-red-600 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-8">
                    <p class="text-gray-500 text-sm">No secondary goals set. Add alternative career paths or milestone goals.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Add Secondary Goal Form -->
    <div x-show="showSecondaryGoalForm" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white rounded-lg shadow" 
         x-cloak>
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Add Secondary Career Goal</h2>
        </div>
        
        <form hx-post="{% url 'add_career_goal' %}"
              hx-vals='{"is_primary": "false"}'
              hx-target="#secondary-goals-container"
              hx-swap="beforeend"
              hx-on::after-request="if(event.detail.successful) { this.reset(); showSecondaryGoalForm = false; }"
              class="p-6 space-y-6">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="secondary_goal_title" class="block text-sm font-medium text-gray-700 mb-2">
                        Goal Title
                    </label>
                    <input type="text" 
                           id="secondary_goal_title" 
                           name="title"
                           placeholder="e.g., Technical Lead, Startup Founder"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div>
                    <label for="secondary_goal_timeline" class="block text-sm font-medium text-gray-700 mb-2">
                        Timeline
                    </label>
                    <select id="secondary_goal_timeline" 
                            name="timeline"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select Timeline</option>
                        <option value="immediate">Immediate (0-1 year)</option>
                        <option value="short_term">Short-term (1-3 years)</option>
                        <option value="medium_term">Medium-term (3-5 years)</option>
                        <option value="long_term">Long-term (5+ years)</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label for="secondary_goal_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="secondary_goal_description" 
                          name="description"
                          rows="3"
                          placeholder="Describe this career goal..."
                          required
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
            </div>
            
            <div>
                <label for="secondary_goal_industry" class="block text-sm font-medium text-gray-700 mb-2">
                    Industry
                </label>
                <select id="secondary_goal_industry" 
                        name="industry"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">Select Industry</option>
                    <option value="technology">Technology</option>
                    <option value="finance">Finance</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="education">Education</option>
                    <option value="consulting">Consulting</option>
                    <option value="research">Research</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button type="button" 
                        @click="showSecondaryGoalForm = false"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                    Add Goal
                    <div class="htmx-indicator ml-2">
                        <svg class="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function careerGoalsManager() {
    return {
        showPrimaryGoalForm: {{ not primary_goal|yesno:"false,true" }},
        showSecondaryGoalForm: false,
        
        init() {
            // Initialize any required setup
        }
    };
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    [x-cloak] {
        display: none !important;
    }
</style>
{% endblock %}
