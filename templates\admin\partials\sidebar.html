<aside class="nav-sidebar"
       :class="isSidebarOpen ? 'nav-sidebar-expanded' : 'nav-sidebar-collapsed'">
    <div class="h-full px-3 py-4">
        <!-- Sidebar Header -->
        <div class="flex h-16 flex-shrink-0 items-center" :class="isSidebarOpen ? 'justify-between px-4' : 'justify-center px-2'">
            <a href="{% url 'admin_dashboard' %}" class="flex items-center" x-show="isSidebarOpen" x-transition:opacity x-cloak>
                <div class="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                </div>
                <span class="ml-2 text-white font-bold text-xl">Admin Panel</span>
            </a>
            <button @click="isSidebarOpen = !isSidebarOpen"
                    class="mobile-nav-toggle"
                    :class="isSidebarOpen ? 'p-2' : 'p-3'"
                    aria-label="Toggle sidebar">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>
        <!-- Navigation Menu -->
        <nav class="mt-8 px-2 space-y-2">
            <div class="relative group">
                <a href="{% url 'admin_dashboard' %}"
                   class="nav-item"
                   up-target="main"
                   up-transition="cross-fade">
                    <svg class="nav-icon"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                    </svg>
                    <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Dashboard</span>
                </a>
                <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Dashboard</div>
            </div>

            <div class="relative group">
                <a href="{% url 'admin_students' %}"
                   class="nav-item"
                   up-target="main"
                   up-transition="cross-fade">
                    <svg class="nav-icon"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Students</span>
                </a>
                <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Students</div>
            </div>
            <div class="relative group">
                <a href="{% url 'admin_courses' %}"
                   class="nav-item"
                   up-target="main"
                   up-transition="cross-fade">
                    <svg class="nav-icon"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Courses</span>
                </a>
                <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Courses</div>
            </div>

            <div class="relative group">
                <a href="{% url 'admin_advising' %}"
                   class="nav-item"
                   up-target="main"
                   up-transition="cross-fade">
                    <svg class="nav-icon"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Advising</span>
                </a>
                <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Advising</div>
            </div>

            <div class="relative group">
                <a href="{% url 'admin_reports' %}"
                   class="nav-item"
                   up-target="main"
                   up-transition="cross-fade">
                    <svg class="nav-icon"
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="ml-3 flex-1 whitespace-nowrap" x-show="isSidebarOpen">Reports</span>
                </a>
                <div x-show="!isSidebarOpen" class="nav-tooltip" x-cloak>Reports</div>
            </div>
        </nav>
    </div>
</aside>
