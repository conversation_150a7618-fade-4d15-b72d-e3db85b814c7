<aside class="bg-gray-800 text-white transition-all duration-300 z-20"
       :class="isSidebarOpen ? 'w-64' : 'w-20'">
    <div class="h-full px-3 py-4">
        <div class="p-4 text-center">
            <a href="{% url 'admin_dashboard' %}">
                <div class="w-12 h-12 mx-auto bg-gray-700 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>
                </div>
                <h2 class="text-lg font-semibold text-white mt-2" x-show="isSidebarOpen">Admin Menu</h2>
            </a>
        </div>
        <nav class="mt-4">
            <div class="relative group">
                <a href="{% url 'admin_dashboard' %}" 
                   class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center"
                   up-target="main" 
                   up-transition="cross-fade">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                    </svg>
                    <span class="ml-3" x-show="isSidebarOpen">Dashboard</span>
                </a>
                <div x-show="!isSidebarOpen" 
                     class="absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-40">
                    Dashboard
                </div>
            </div>
            <div class="relative group">
                <a href="{% url 'admin_students' %}" 
                   class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center"
                   up-target="main" 
                   up-transition="cross-fade">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="ml-3" x-show="isSidebarOpen">Students</span>
                </a>
                <div x-show="!isSidebarOpen" 
                     class="absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-40">
                    Students
                </div>
            </div>
            <div class="relative group">
                <a href="{% url 'admin_courses' %}" 
                   class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center"
                   up-target="main" 
                   up-transition="cross-fade">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                    </svg>
                    <span class="ml-3" x-show="isSidebarOpen">Courses</span>
                </a>
                <div x-show="!isSidebarOpen" 
                     class="absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-40">
                    Courses
                </div>
            </div>
            <div class="relative group">
                <a href="{% url 'admin_advising' %}" 
                   class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center"
                   up-target="main" 
                   up-transition="cross-fade">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="ml-3" x-show="isSidebarOpen">Advising</span>
                </a>
                <div x-show="!isSidebarOpen" 
                     class="absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-40">
                    Advising
                </div>
            </div>
            <div class="relative group">
                <a href="{% url 'admin_reports' %}" 
                   class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex items-center"
                   up-target="main" 
                   up-transition="cross-fade">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400 transition duration-75 group-hover:text-white" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.976 5.976 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-1.106-1.106A6.003 6.003 0 004 10c0 .639.098 1.255.274 1.838l1.884-1.721zm.556-6.032L6.239 3.66A6.003 6.003 0 0110 4c.639 0 1.255.098 1.838.274l-2.898 2.898a4.001 4.001 0 00-2.126.053z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-3" x-show="isSidebarOpen">Reports</span>
                </a>
                <div x-show="!isSidebarOpen" 
                     class="absolute left-full ml-2 p-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-40">
                    Reports
                </div>
            </div>
        </nav>
    </div>
</aside>
