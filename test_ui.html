<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Test - CourseRec Design System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="course_matcher/static/css/styles.css">
</head>
<body class="bg-gray-100" x-data="{ isSidebarOpen: true, isMobileMenuOpen: false }">
    
    <!-- Test Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 p-4 mb-8">
        <h1 class="heading-primary">CourseRec Design System Test</h1>
        <p class="text-muted">Testing the unified design components</p>
    </div>
    
    <!-- Test Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Stats Cards Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Stats Cards</h2>
            <div class="dashboard-grid">
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-primary">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Total Students</dt>
                                <dd class="stat-value">1,234</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-success">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Total Courses</dt>
                                <dd class="stat-value">567</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-warning">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Active Sessions</dt>
                                <dd class="stat-value">89</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card-gradient">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon stat-icon-danger">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="stat-label">Pending Reports</dt>
                                <dd class="stat-value">12</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Buttons Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Buttons</h2>
            <div class="flex flex-wrap gap-4">
                <button class="btn btn-primary">Primary Button</button>
                <button class="btn btn-secondary">Secondary Button</button>
                <button class="btn btn-outline">Outline Button</button>
                <button class="btn btn-success">Success Button</button>
                <button class="btn btn-warning">Warning Button</button>
                <button class="btn btn-danger">Danger Button</button>
            </div>
        </div>
        
        <!-- Cards Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Cards</h2>
            <div class="content-grid">
                <div class="card card-hover">
                    <div class="card-header">
                        <h3 class="heading-tertiary">Card Title</h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">This is a test card with hover effects and proper spacing.</p>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-primary btn-sm">Action</button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <h3 class="heading-tertiary mb-4">Simple Card</h3>
                        <p class="text-muted">A simple card without header or footer.</p>
                    </div>
                </div>
                
                <div class="card card-interactive">
                    <div class="card-body">
                        <h3 class="heading-tertiary mb-4">Interactive Card</h3>
                        <p class="text-muted">This card has interactive hover effects.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Forms</h2>
            <div class="card">
                <div class="card-body">
                    <form class="space-y-6">
                        <div class="form-group">
                            <label for="test-input" class="form-label">Test Input</label>
                            <input type="text" id="test-input" class="form-input" placeholder="Enter some text">
                        </div>
                        
                        <div class="form-group">
                            <label for="test-select" class="form-label">Test Select</label>
                            <select id="test-select" class="form-select">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="form-checkbox">
                                <span class="ml-2 text-sm text-gray-700">Test checkbox</span>
                            </label>
                        </div>
                        
                        <div class="flex justify-end space-x-4">
                            <button type="button" class="btn btn-secondary">Cancel</button>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Badges Test -->
        <div class="mb-8">
            <h2 class="heading-secondary mb-4">Badges</h2>
            <div class="flex flex-wrap gap-2">
                <span class="badge badge-primary">Primary</span>
                <span class="badge badge-success">Success</span>
                <span class="badge badge-warning">Warning</span>
                <span class="badge badge-danger">Danger</span>
                <span class="badge badge-gray">Gray</span>
            </div>
        </div>
        
    </div>
    
</body>
</html>
