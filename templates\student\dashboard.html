{% extends 'student/base.html' %}
{% load course_matcher_tags %}

{% block title %}Dashboard - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" 
     x-data="dashboard()" 
     x-init="init()">
    
    <!-- Welcome Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ user.first_name|default:user.username }}!</h1>
        <p class="mt-2 text-gray-600">Track your academic progress and discover personalized course recommendations.</p>
    </div>

    <!-- Overview Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="p-6 bg-gradient-to-br from-white to-blue-50 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center shadow-inner">
                        <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Overall GPA</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ student.gpa|floatformat:2 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="p-6 bg-gradient-to-br from-white to-green-50 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center shadow-inner">
                        <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M12 6V4l-8 8v2h2l8-8zm2-2l2-2v2h-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Credits Earned</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ student.total_credits }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="p-6 bg-gradient-to-br from-white to-purple-50 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center shadow-inner">
                        <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Courses Completed</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ completed_courses_count }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="p-6 bg-gradient-to-br from-white to-yellow-50 rounded-lg shadow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center shadow-inner">
                        <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">New Recommendations</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ recommendations_count }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Panels -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Activities -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Recent Activities</h2>
            </div>
            <div class="p-6" id="recent-activities">
                {% if recent_activities %}
                    <div class="space-y-4">
                        {% for activity in recent_activities %}
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900">{{ activity.description }}</p>
                                <p class="text-xs text-gray-500">{{ activity.created_at|timesince }} ago</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-gray-500 py-16">
                        <svg class="mx-auto h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-4 text-lg font-semibold text-gray-700">No Recent Activities</h3>
                        <p class="mt-2 text-base text-gray-500">Your recent actions will appear here.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 gap-4">
                    <a href="{% url 'academic_records' %}" 
                       class="flex items-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg transform transition-all duration-300 hover:shadow-lg hover:scale-105">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-blue-900">Manage Academic Records</p>
                            <p class="text-xs text-blue-700">Add or update your course history</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'student_interests' %}" 
                       class="flex items-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg transform transition-all duration-300 hover:shadow-lg hover:scale-105">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-900">Update Interests</p>
                            <p class="text-xs text-green-700">Set your academic and personal interests</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'career_goals' %}" 
                       class="flex items-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg transform transition-all duration-300 hover:shadow-lg hover:scale-105">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-purple-900">Set Career Goals</p>
                            <p class="text-xs text-purple-700">Define your career objectives</p>
                        </div>
                    </a>
                    
                    <a href="{% url 'recommendations' %}" 
                       class="flex items-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg transform transition-all duration-300 hover:shadow-lg hover:scale-105">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-yellow-900">View Recommendations</p>
                            <p class="text-xs text-yellow-700">Discover personalized course suggestions</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Overview -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Academic Progress</h2>
        </div>
        <div class="p-6">
            <div class="mb-4">
                <div class="flex justify-between text-sm">
                    <span class="font-medium text-gray-700">Credits toward graduation</span>
                    <span class="text-gray-500">{{ student.total_credits }}/120</span>
                </div>
                <div class="mt-2 bg-gray-200 rounded-full h-4">
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-4 rounded-full text-center text-white text-xs font-bold leading-4"
                         style="width: {{ student.total_credits|percentage:120 }}%">
                         {{ student.total_credits|percentage:120 }}%
                    </div>
                </div>
            </div>
            
            {% if student.major %}
            <div class="mt-6">
                <h3 class="text-sm font-medium text-gray-700 mb-3">Major: {{ student.major }}</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-600">Core Requirements</div>
                        <div class="text-2xl font-bold text-gray-900">{{ core_completed }}/{{ core_required }}</div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-600">Electives</div>
                        <div class="text-2xl font-bold text-gray-900">{{ electives_completed }}/{{ electives_required }}</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function dashboard() {
    return {
        refreshInterval: null,
        
        init() {
            // Auto-refresh recent activities every 5 minutes
            this.refreshInterval = setInterval(() => {
                htmx.trigger('#recent-activities', 'refresh');
            }, 300000);
        },
        
        destroy() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        }
    };
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
</style>
{% endblock %}
