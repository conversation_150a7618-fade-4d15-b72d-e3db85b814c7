{% extends 'student/base.html' %}

{% block title %}Interests - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" 
     x-data="interestsManager()" 
     x-init="init()">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Academic Interests</h1>
        <p class="mt-2 text-gray-600">Manage your academic and personal interests to get better course recommendations.</p>
    </div>

    <!-- Current Interests Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">Current Interests</h2>
            <button @click="showAddForm = !showAddForm"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Interest
            </button>
        </div>
        
        <div class="p-6">
            <div id="interests-container" class="space-y-4">
                {% for interest in student_interests %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                     x-data="{ editing: false, editValue: '{{ interest.name }}' }">
                    <div class="flex items-center space-x-3 flex-1">
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                       {% if interest.category == 'academic' %}bg-blue-100 text-blue-800
                                       {% elif interest.category == 'personal' %}bg-green-100 text-green-800
                                       {% elif interest.category == 'career' %}bg-purple-100 text-purple-800
                                       {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ interest.get_category_display }}
                            </span>
                        </div>
                        <div class="flex-1" x-show="!editing">
                            <h3 class="text-sm font-medium text-gray-900">{{ interest.name }}</h3>
                            {% if interest.description %}
                            <p class="text-xs text-gray-500 mt-1">{{ interest.description }}</p>
                            {% endif %}
                        </div>
                        <div class="flex-1" x-show="editing" x-cloak>
                            <input type="text" 
                                   x-model="editValue"
                                   @keydown.enter="updateInterest({{ interest.id }}, editValue); editing = false"
                                   @keydown.escape="editing = false; editValue = '{{ interest.name }}'"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="editing = !editing"
                                class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button hx-delete="{% url 'delete_interest' interest.id %}"
                                hx-confirm="Are you sure you want to remove this interest?"
                                hx-target="closest div"
                                hx-swap="outerHTML"
                                class="text-red-400 hover:text-red-600 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No interests added yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Start by adding your first interest to get personalized recommendations.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Add Interest Form -->
    <div x-show="showAddForm" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="bg-white rounded-lg shadow mb-8" 
         x-cloak>
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Add New Interest</h2>
        </div>
        
        <form hx-post="{% url 'add_interest' %}"
              hx-target="#interests-container"
              hx-swap="beforeend"
              hx-on::after-request="if(event.detail.successful) { this.reset(); showAddForm = false; }"
              class="p-6 space-y-6">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="interest_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Interest Name
                    </label>
                    <input type="text" 
                           id="interest_name" 
                           name="name"
                           placeholder="e.g., Machine Learning, Web Development"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div>
                    <label for="interest_category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category
                    </label>
                    <select id="interest_category" 
                            name="category"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select Category</option>
                        <option value="academic">Academic</option>
                        <option value="personal">Personal</option>
                        <option value="career">Career</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label for="interest_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description (Optional)
                </label>
                <textarea id="interest_description" 
                          name="description"
                          rows="3"
                          placeholder="Briefly describe your interest or specific areas you'd like to explore..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button type="button" 
                        @click="showAddForm = false"
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                    Cancel
                </button>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Interest
                    <div class="htmx-indicator ml-2">
                        <svg class="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </button>
            </div>
        </form>
    </div>

    <!-- Interest Suggestions -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Suggested Interests</h2>
            <p class="text-sm text-gray-600 mt-1">Popular interests based on your major and academic history</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {% for suggestion in suggested_interests %}
                <button hx-post="{% url 'add_suggested_interest' %}"
                        hx-vals='{"name": "{{ suggestion.name }}", "category": "{{ suggestion.category }}"}'
                        hx-target="#interests-container"
                        hx-swap="beforeend"
                        hx-on::after-request="this.style.display = 'none'"
                        class="flex items-center px-3 py-2 bg-gray-100 hover:bg-primary-100 rounded-lg text-sm font-medium text-gray-700 hover:text-primary-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ suggestion.name }}
                </button>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500 text-sm">No suggestions available. Add your first interest to get personalized suggestions.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
function interestsManager() {
    return {
        showAddForm: false,
        
        init() {
            // Initialize any required setup
        },
        
        updateInterest(interestId, newName) {
            htmx.ajax('PATCH', `/student/interests/${interestId}/`, {
                values: { name: newName },
                target: '#interests-container',
                swap: 'innerHTML'
            });
        }
    };
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    [x-cloak] {
        display: none !important;
    }
</style>
{% endblock %}
