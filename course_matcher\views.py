from django.shortcuts import render
from django.http import HttpResponse
from django.db import models
from .models import StudentProfile, Course

def home(request):
    # This view is not fully implemented, returning a simple response for now.
    return render(request, 'student/dashboard.html')

def student_academic_records(request):
    return render(request, 'student/academic_records.html')

def student_interests(request):
    return render(request, 'student/interests.html')

def career_goals(request):
    return render(request, 'student/career_goals.html')

def recommendations(request):
    return render(request, 'student/recommendations.html')

# Placeholder HTMX action views
def bookmark_course(request, course_id):
    return HttpResponse(status=204)

def delete_interest(request, interest_id):
    return HttpResponse(status=204)

def add_interest(request):
    return HttpResponse(status=204)

def add_suggested_interest(request):
    return HttpResponse(status=204)

def update_career_goal(request, goal_id):
    return HttpResponse(status=204)

def add_career_goal(request):
    return HttpResponse(status=204)

def delete_career_goal(request, goal_id):
    return HttpResponse(status=204)

def delete_academic_record(request, record_id):
    return HttpResponse(status=204)

def add_academic_record(request):
    return HttpResponse(status=204)

# Admin Views
def admin_dashboard(request):
    return render(request, 'admin/dashboard.html')

def admin_students(request):
    return render(request, 'admin/students.html')

def admin_courses(request):
    return render(request, 'admin/courses.html')

def admin_advising(request):
    return render(request, 'admin/advising.html')

def admin_reports(request):
    return render(request, 'admin/reports.html')

# HTMX API Views
def recent_activity_api(request):
    # This is a placeholder; a real implementation would query the database
    # for recent advising sessions, student registrations, etc.
    return HttpResponse("""<ul>
            <li class='border-b py-2'>Student <strong>John Doe</strong> registered.</li>
            <li class='border-b py-2'>Course <strong>'Intro to Python'</strong> was updated.</li>
            <li class='py-2'>New advising session created for <strong>Jane Smith</strong>.</li>
        </ul>""")

def student_list_api(request):
    students = StudentProfile.objects.select_related('user', 'major').all()[:50] # Limiting for performance
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def search_students_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        # A more comprehensive search would query across multiple fields
        students = StudentProfile.objects.select_related('user', 'major').filter(
            models.Q(user__first_name__icontains=search_text) |
            models.Q(user__last_name__icontains=search_text) |
            models.Q(student_id__icontains=search_text) |
            models.Q(major__name__icontains=search_text)
        )[:50]
    else:
        students = StudentProfile.objects.select_related('user', 'major').all()[:50]
        
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def course_list_api(request):
    courses = Course.objects.select_related('department').all()[:50]
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})

def search_courses_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        courses = Course.objects.select_related('department').filter(
            models.Q(name__icontains=search_text) |
            models.Q(code__icontains=search_text) |
            models.Q(department__name__icontains=search_text)
        )[:50]
    else:
        courses = Course.objects.select_related('department').all()[:50]
        
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})
