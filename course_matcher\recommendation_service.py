"""
Course Recommendation Service
Implements classification, knowledge-based, and content-based recommendation algorithms
"""
import json
from typing import List, Dict, Tuple
from django.db.models import Q, Count, Avg
from .models import Course, StudentProfile, AcademicRecord, Recommendation, Department
import math
from collections import Counter


class RecommendationEngine:
    
    def __init__(self):
        self.weights = {
            'classification': 0.4,
            'knowledge': 0.3,
            'content': 0.3
        }
    
    def get_recommendations(self, student: StudentProfile, limit: int = 5) -> List[Recommendation]:
        """
        Generate hybrid recommendations combining all three approaches
        """
        # Get candidates (courses not yet taken)
        taken_courses = set(student.academicrecord_set.values_list('course_id', flat=True))
        available_courses = Course.objects.exclude(id__in=taken_courses)
        
        recommendations = []
        
        for course in available_courses:
            # Get scores from each approach
            classification_score = self._classification_based_score(student, course)
            knowledge_score = self._knowledge_based_score(student, course)
            content_score = self._content_based_score(student, course)
            
            # Calculate hybrid score
            hybrid_score = (
                self.weights['classification'] * classification_score +
                self.weights['knowledge'] * knowledge_score +
                self.weights['content'] * content_score
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                student, course, classification_score, knowledge_score, content_score
            )
            
            # Create or update recommendation
            recommendation, created = Recommendation.objects.get_or_create(
                student=student,
                course=course,
                defaults={
                    'confidence_score': hybrid_score,
                    'recommendation_type': 'hybrid',
                    'reasoning': reasoning
                }
            )
            
            if not created:
                recommendation.confidence_score = hybrid_score
                recommendation.reasoning = reasoning
                recommendation.save()
            
            recommendations.append(recommendation)
        
        # Sort by confidence score and return top recommendations
        recommendations.sort(key=lambda x: x.confidence_score, reverse=True)
        return recommendations[:limit]
    
    def _classification_based_score(self, student: StudentProfile, course: Course) -> float:
        """
        Classification-based: Recommend based on similar students' choices
        """
        # Find students with similar academic performance and interests
        similar_students = StudentProfile.objects.filter(
            year=student.year,
            major=student.major
        ).exclude(id=student.id)
        
        # Filter by GPA similarity (within 0.5 points)
        student_gpa = student.gpa
        similar_students = [
            s for s in similar_students 
            if abs(s.gpa - student_gpa) <= 0.5
        ]
        
        if not similar_students:
            return 0.5  # Default score
        
        # Count how many similar students took this course and performed well
        good_performance_count = 0
        total_count = 0
        
        for similar_student in similar_students:
            record = AcademicRecord.objects.filter(
                student=similar_student,
                course=course
            ).first()
            
            if record and record.grade:
                total_count += 1
                if record.grade in ['A', 'A-', 'B+', 'B']:
                    good_performance_count += 1
        
        if total_count == 0:
            return 0.3  # Low score if no similar students took the course
        
        return good_performance_count / total_count
    
    def _knowledge_based_score(self, student: StudentProfile, course: Course) -> float:
        """
        Knowledge-based: Recommend based on academic rules and prerequisites
        """
        score = 0.5  # Base score
        
        # Check prerequisites completion
        prerequisites = course.prerequisites.all()
        completed_prereqs = 0
        
        for prereq in prerequisites:
            if student.academicrecord_set.filter(
                course=prereq,
                grade__in=['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C']
            ).exists():
                completed_prereqs += 1
        
        if prerequisites.exists():
            prereq_completion_rate = completed_prereqs / prerequisites.count()
            score += 0.3 * prereq_completion_rate
        
        # Difficulty matching
        if course.difficulty == student.preferred_difficulty:
            score += 0.15
        elif abs(self._difficulty_to_int(course.difficulty) - 
                self._difficulty_to_int(student.preferred_difficulty)) == 1:
            score += 0.1
        
        # Major alignment
        if course.department == student.major:
            score += 0.2
        
        # Year appropriateness
        year_course_mapping = {
            'freshman': ['beginner'],
            'sophomore': ['beginner', 'intermediate'],
            'junior': ['intermediate', 'advanced'],
            'senior': ['intermediate', 'advanced'],
            'graduate': ['advanced']
        }
        
        if course.difficulty in year_course_mapping.get(student.year, []):
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _content_based_score(self, student: StudentProfile, course: Course) -> float:
        """
        Content-based: Recommend based on course topics and student interests
        """
        if not student.interests or not course.topics:
            return 0.3  # Default score
        
        # Calculate topic similarity using Jaccard similarity
        student_interests = set(student.interests)
        course_topics = set(course.topics)
        
        intersection = len(student_interests.intersection(course_topics))
        union = len(student_interests.union(course_topics))
        
        if union == 0:
            return 0.3
        
        jaccard_similarity = intersection / union
        
        # Boost score based on student's past performance in related topics
        related_courses = Course.objects.filter(
            topics__overlap=course.topics
        ).exclude(id=course.id)
        
        performance_boost = 0
        total_related_records = 0
        good_performance_count = 0
        
        for related_course in related_courses:
            record = student.academicrecord_set.filter(course=related_course).first()
            if record and record.grade:
                total_related_records += 1
                if record.grade in ['A', 'A-', 'B+', 'B']:
                    good_performance_count += 1
        
        if total_related_records > 0:
            performance_boost = 0.2 * (good_performance_count / total_related_records)
        
        return min(jaccard_similarity + performance_boost, 1.0)
    
    def _difficulty_to_int(self, difficulty: str) -> int:
        """Convert difficulty string to integer for comparison"""
        mapping = {'beginner': 1, 'intermediate': 2, 'advanced': 3}
        return mapping.get(difficulty, 2)
    
    def _generate_reasoning(self, student: StudentProfile, course: Course, 
                          classification_score: float, knowledge_score: float, 
                          content_score: float) -> str:
        """Generate human-readable reasoning for the recommendation"""
        reasons = []
        
        # Classification reasoning
        if classification_score > 0.7:
            reasons.append("Similar students with comparable academic performance have succeeded in this course")
        elif classification_score > 0.5:
            reasons.append("Some students with similar backgrounds have taken this course")
        
        # Knowledge reasoning
        if knowledge_score > 0.8:
            reasons.append("You meet all prerequisites and this course aligns well with your academic profile")
        elif knowledge_score > 0.6:
            reasons.append("This course is appropriate for your academic level and major")
        
        # Content reasoning
        if content_score > 0.7:
            reasons.append("This course strongly matches your stated interests")
        elif content_score > 0.5:
            reasons.append("This course covers topics related to your interests")
        
        # Major alignment
        if course.department == student.major:
            reasons.append("This course is in your major field of study")
        
        # Prerequisites
        prereq_count = course.prerequisites.count()
        if prereq_count > 0:
            completed_prereqs = sum(1 for prereq in course.prerequisites.all() 
                                  if student.academicrecord_set.filter(
                                      course=prereq, 
                                      grade__in=['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C']
                                  ).exists())
            if completed_prereqs == prereq_count:
                reasons.append("You have completed all required prerequisites")
            elif completed_prereqs > 0:
                reasons.append(f"You have completed {completed_prereqs} of {prereq_count} prerequisites")
        
        return ". ".join(reasons) if reasons else "This course may be suitable based on your academic profile"


def generate_recommendations_for_student(student_id: int, limit: int = 5) -> List[Recommendation]:
    """
    Convenience function to generate recommendations for a student
    """
    try:
        student = StudentProfile.objects.get(id=student_id)
        engine = RecommendationEngine()
        return engine.get_recommendations(student, limit)
    except StudentProfile.DoesNotExist:
        return []
